{"version": "0.2.0", "configurations": [{"name": "Debug LaCAM3 - Small Test", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/main.exe", "args": ["-i", "assets/random-32-32-10-random-1.scen", "-m", "assets/random-32-32-10.map", "-N", "5", "-v", "3", "-t", "10000000"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "build-debug"}, {"name": "Debug LaCAM3 - Medium Test", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/main.exe", "args": ["-i", "assets/random-32-32-10-random-1.scen", "-m", "assets/random-32-32-10.map", "-N", "50", "-v", "2", "-t", "30"], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build-debug"}, {"name": "Debug Tests", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/test_planner.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build-debug"}]}