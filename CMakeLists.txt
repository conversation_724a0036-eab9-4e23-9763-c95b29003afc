cmake_minimum_required(VERSION 3.16)
project(lacam-project CXX)

# Set default build type to Debug if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Add debug flags for Debug builds
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
endif()

add_subdirectory(./lacam3)

# Configure argparse library (disable tests and samples for faster build)
set(ARGPARSE_BUILD_TESTS OFF CACHE BOOL "Build tests" FORCE)
set(ARGPARSE_BUILD_SAMPLES OFF CACHE BOOL "Build samples" FORCE)
set(ARGPARSE_INSTALL OFF CACHE BOOL "Include an install target" FORCE)
add_subdirectory(./third_party/argparse)

add_executable(main main.cpp)
target_compile_features(main PUBLIC cxx_std_17)
target_link_libraries(main lacam3 argparse::argparse)

# test
enable_testing()
file(GLOB TEST_FILES "./tests/test_*.cpp")
foreach(file ${TEST_FILES})
  string(REGEX MATCH "test\_[^\.]+" name "${file}")
  add_executable(${name} ${file})
  target_link_libraries(${name} lacam3)
  add_test(${name} ${name})
endforeach()
