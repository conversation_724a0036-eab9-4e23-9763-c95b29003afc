{"cmake.buildDirectory": "${workspaceFolder}/build", "cmake.configureOnOpen": true, "cmake.debugConfig": {"args": ["-i", "assets/random-32-32-10-random-1.scen", "-m", "assets/random-32-32-10.map", "-N", "5", "-v", "3"]}, "files.associations": {"*.hpp": "cpp", "*.cpp": "cpp", "*.h": "c", "bitset": "cpp"}, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.intelliSenseMode": "windows-gcc-x64", "C_Cpp.default.compilerPath": "D:/Program Files/mingw64/bin/g++.exe", "C_Cpp.clang_format_style": "{ BasedOnStyle: Google, IndentWidth: 2, ColumnLimit: 80 }", "editor.formatOnSave": true, "editor.rulers": [80, 100], "debug.console.fontSize": 12, "debug.console.fontFamily": "Consolas, 'Courier New', monospace"}