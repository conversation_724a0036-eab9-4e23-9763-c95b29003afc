# Created by https://www.toptal.com/developers/gitignore/api/julia
# Edit at https://www.toptal.com/developers/gitignore?templates=julia

### Julia ###
# Files generated by invoking <PERSON> with --code-coverage
*.jl.cov
*.jl.*.cov

# Files generated by invoking Julia with --track-allocation
*.jl.mem

# System-specific files and directories generated by the BinaryProvider and BinDeps packages
# They contain absolute paths specific to the host computer, and so should not be committed
deps/deps.jl
deps/build.log
deps/downloads/
deps/usr/
deps/src/

# Build artifacts for creating documentation generated by the Documenter package
docs/build/
docs/site/

# File generated by <PERSON>kg, the package manager, based on a corresponding Project.toml
# It records a fixed state of all packages used by the project. As such, it should not be
# committed for packages, but should be committed for applications that require a static
# environment.
Manifest.toml

# End of https://www.toptal.com/developers/gitignore/api/julia

**.DS_Store
**/local/
