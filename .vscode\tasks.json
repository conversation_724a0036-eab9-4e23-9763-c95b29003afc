{"version": "2.0.0", "tasks": [{"label": "cmake-configure-debug", "type": "shell", "command": "cmake", "args": ["-B", "build", "-G", "MinGW Makefiles", "-DCMAKE_BUILD_TYPE=Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "cmake-configure-release", "type": "shell", "command": "cmake", "args": ["-B", "build", "-DCMAKE_BUILD_TYPE=Release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "build-debug", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Debug", "--target", "main"], "group": {"kind": "build", "isDefault": true}, "dependsOn": "cmake-configure-debug", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$msCompile"]}, {"label": "build-release", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Release"], "group": "build", "dependsOn": "cmake-configure-release", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"]}, {"label": "clean", "type": "shell", "command": "cmake", "args": ["--build", "build", "--target", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "run-tests", "type": "shell", "command": "ctest", "args": ["--test-dir", "./build", "--verbose"], "group": "test", "dependsOn": "build-debug", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}