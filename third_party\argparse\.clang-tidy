Checks:
  -*,
  clang-analyzer-*,
  cppcoreguidelines-avoid-c-arrays,
  cppcoreguidelines-special-member-functions,
  readability-*,

CheckOptions:
  - { key: readability-identifier-naming.ClassCase, value: CamelCase }
  - { key: readability-identifier-naming.ConstexprVariableCase, value: lower_case }
  - { key: readability-identifier-naming.ConstexprVariableIgnoredRegexp, value: "^Is.+" }
  - { key: readability-identifier-naming.FunctionCase, value: lower_case }
  - { key: readability-identifier-naming.NamespaceCase, value: lower_case }
  - { key: readability-identifier-naming.ParameterCase, value: lower_case }
  - { key: readability-identifier-naming.PrivateMemberCase, value: lower_case }
  - { key: readability-identifier-naming.PrivateMemberPrefix, value: m_ }
  - { key: readability-identifier-naming.StructCase, value: CamelCase }
  - { key: readability-identifier-naming.StructIgnoredRegexp, value: "parse_number" }
  - { key: readability-identifier-naming.VariableCase, value: lower_case }

HeaderFilterRegex: 'argparse/.+\.hpp'
