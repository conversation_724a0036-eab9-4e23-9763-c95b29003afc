{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/lacam3/include", "${workspaceFolder}/third_party/**"], "defines": ["_DEBUG", "DEBUG", "UNICODE", "_UNICODE"], "windowsSdkVersion": "10.0.19041.0", "compilerPath": "D:/Program Files/mingw64/bin/gcc.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "configurationProvider": "ms-vscode.cmake-tools", "compilerArgs": ["-Wall", "-g"]}], "version": 4}