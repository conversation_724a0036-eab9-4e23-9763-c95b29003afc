# Secure workflow with access to repository secrets and GitHub token
# for posting analysis results.
name: Post the Tidy analysis results

on:
  workflow_run:
    workflows: [ "Tidy analysis" ]
    types: [ completed ]

jobs:

  clang-tidy-results:

    # Trigger the job only if the previous (insecure) workflow completed successfully
    if: ${{ github.event.workflow_run.event == 'pull_request' && github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-20.04

    steps:

    - name: Download analysis results
      uses: actions/github-script@v3.1.0
      with:
        script: |
          let artifacts = await github.actions.listWorkflowRunArtifacts({
              owner: context.repo.owner,
              repo: context.repo.repo,
              run_id: ${{github.event.workflow_run.id }},
          });
          let matchArtifact = artifacts.data.artifacts.filter((artifact) => {
              return artifact.name == "clang-tidy-result"
          })[0];
          let download = await github.actions.downloadArtifact({
              owner: context.repo.owner,
              repo: context.repo.repo,
              artifact_id: matchArtifact.id,
              archive_format: "zip",
          });
          let fs = require("fs");
          fs.writeFileSync("${{github.workspace}}/clang-tidy-result.zip", Buffer.from(download.data));

    - name: Set environment variables
      run: |
        mkdir clang-tidy-result
        unzip clang-tidy-result.zip -d clang-tidy-result
        echo "pr_id=$(cat clang-tidy-result/pr-id.txt)" >> $GITHUB_ENV
        echo "pr_head_repo=$(cat clang-tidy-result/pr-head-repo.txt)" >> $GITHUB_ENV
        echo "pr_head_ref=$(cat clang-tidy-result/pr-head-ref.txt)" >> $GITHUB_ENV

    - uses: actions/checkout@v3
      with:
        repository: ${{ env.pr_head_repo }}
        ref: ${{ env.pr_head_ref }}
        persist-credentials: false

    - name: Redownload analysis results
      uses: actions/github-script@v3.1.0
      with:
        script: |
          let artifacts = await github.actions.listWorkflowRunArtifacts({
              owner: context.repo.owner,
              repo: context.repo.repo,
              run_id: ${{github.event.workflow_run.id }},
          });
          let matchArtifact = artifacts.data.artifacts.filter((artifact) => {
              return artifact.name == "clang-tidy-result"
          })[0];
          let download = await github.actions.downloadArtifact({
              owner: context.repo.owner,
              repo: context.repo.repo,
              artifact_id: matchArtifact.id,
              archive_format: "zip",
          });
          let fs = require("fs");
          fs.writeFileSync("${{github.workspace}}/clang-tidy-result.zip", Buffer.from(download.data));

    - name: Extract analysis results
      run: |
        mkdir clang-tidy-result
        unzip clang-tidy-result.zip -d clang-tidy-result

    - name: Run clang-tidy-pr-comments action
      uses: platisd/clang-tidy-pr-comments@master
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        clang_tidy_fixes: clang-tidy-result/fixes.yml
        pull_request_id: ${{ env.pr_id }}
